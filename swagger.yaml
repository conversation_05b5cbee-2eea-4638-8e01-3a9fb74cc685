openapi: 3.0.3
info:
  title: Stockaty API
  description: |
    A comprehensive API for managing stock media downloads with subscription-based credit system.

    ## Authentication
    Most endpoints require authentication via session cookies. After successful login, include the session cookie in subsequent requests.

    ## Credit System
    - Users have subscription plans with allocated credits
    - Each download consumes credits based on site pricing
    - Credits are tracked and managed through the subscription system

    ## Error Handling
    All endpoints return standardized error responses with specific error IDs for client-side handling.
  version: 1.0.0

paths:
  # Authentication Endpoints
  /v1/auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user with email and password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - token
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  description: "Encrypted password (ROT13 + Base64 + Hex encoded)"
                  example: "encrypted_password_string"
                token:
                  type: string
                  description: "Timestamp token for request validation"
                  example: "1704067200"
                remember_me:
                  type: boolean
                  description: "Extend session to 30 days if true, otherwise 24 hours"
                  default: false
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      access_token:
                        type: string
                        description: "JWT token for session management"
                      email:
                        type: string
                        format: email
                      message:
                        type: string
                        example: "Login successful."
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              examples:
                missing_email:
                  value:
                    success: false
                    error:
                      id: 0
                      message: "Email is required."
                missing_password:
                  value:
                    success: false
                    error:
                      id: 1
                      message: "Password is required."
                missing_token:
                  value:
                    success: false
                    error:
                      id: 2
                      message: "Token is required."
                expired_token:
                  value:
                    success: false
                    error:
                      id: 3
                      message: "Token is expired."
                user_not_found:
                  value:
                    success: false
                    error:
                      id: 4
                      message: "The provided email does not exist in our records."
                invalid_password:
                  value:
                    success: false
                    error:
                      id: 5
                      message: "Invalid password. Please check your credentials and try again."

  /v1/auth/register:
    post:
      tags:
        - Authentication
      summary: User registration
      description: Create a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - firstName
                - lastName
                - token
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  description: "Encrypted password (ROT13 + Base64 + Hex encoded)"
                firstName:
                  type: string
                  example: "John"
                lastName:
                  type: string
                  example: "Doe"
                token:
                  type: string
                  description: "Timestamp token for request validation"
      responses:
        "200":
          description: Registration successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      access_token:
                        type: string
                      email:
                        type: string
                        format: email
                      message:
                        type: string
                        example: "register successful."
        "400":
          description: Registration failed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      description: Invalidate current session
      security:
        - sessionAuth: []
      responses:
        "200":
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      access_token:
                        type: string
                      message:
                        type: string
                        example: "logout successful."
        "400":
          description: Logout failed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Credit Management Endpoints
  /v1/credit/subscribe:
    post:
      tags:
        - Credits
      summary: Add subscription
      description: Add a new subscription plan to a user
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - plan_name
              properties:
                email:
                  type: string
                  format: email
                plan_name:
                  type: string
                  example: "premium"
      responses:
        "200":
          description: Subscription added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  account:
                    $ref: "#/components/schemas/UserAccount"
                  subscription:
                    $ref: "#/components/schemas/Subscription"
        "400":
          description: Failed to add subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/credit/upgrade:
    post:
      tags:
        - Credits
      summary: Upgrade subscription
      description: Upgrade user to a new subscription plan
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - plan_name
              properties:
                email:
                  type: string
                  format: email
                plan_name:
                  type: string
                  example: "enterprise"
      responses:
        "200":
          description: Subscription upgraded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  account:
                    $ref: "#/components/schemas/UserAccount"
                  subscription:
                    $ref: "#/components/schemas/Subscription"
        "400":
          description: Failed to upgrade subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/credit/extend:
    post:
      tags:
        - Credits
      summary: Extend subscription
      description: Extend subscription by additional days
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - days
              properties:
                email:
                  type: string
                  format: email
                days:
                  type: integer
                  minimum: 1
                  example: 30
      responses:
        "200":
          description: Subscription extended successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  account:
                    $ref: "#/components/schemas/UserAccount"
                  subscription:
                    $ref: "#/components/schemas/Subscription"
        "400":
          description: Failed to extend subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/credit/delete:
    post:
      tags:
        - Credits
      summary: Delete subscription
      description: Cancel user's subscription
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
              properties:
                email:
                  type: string
                  format: email
      responses:
        "200":
          description: Subscription deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        "400":
          description: Failed to delete subscription
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/credit/analytics:
    get:
      tags:
        - Credits
      summary: Get credit analytics
      description: Retrieve system-wide credit usage statistics
      security:
        - sessionAuth: []
      responses:
        "200":
          description: Credit analytics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  statistics:
                    type: object
                    properties:
                      total_credits_issued:
                        type: integer
                        example: 50000
                      total_credits_used:
                        type: integer
                        example: 25000
                      total_remaining_credits:
                        type: integer
                        example: 25000
                      average_daily_usage:
                        type: number
                        format: float
                        example: 125.5
                      credits_by_plan:
                        type: object
                        additionalProperties:
                          type: integer
                        example:
                          premium: 30000
                          basic: 20000
                      last_updated:
                        type: string
                        format: date-time
                        example: "2024-01-05 14:30:00"
        "400":
          description: Failed to retrieve analytics
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/credit/history:
    get:
      tags:
        - Credits
      summary: Get credit history
      description: Retrieve global credit usage history
      security:
        - sessionAuth: []
      responses:
        "200":
          description: Credit history retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  history:
                    type: array
                    items:
                      $ref: "#/components/schemas/CreditHistoryEntry"
        "400":
          description: Failed to retrieve history
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # User Management Endpoints
  /v1/user/data:
    get:
      tags:
        - Users
      summary: Get user data
      description: Retrieve current user's account and subscription information
      security:
        - sessionAuth: []
      responses:
        "200":
          description: User data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  account:
                    $ref: "#/components/schemas/UserAccount"
                  subscription:
                    $ref: "#/components/schemas/Subscription"
        "400":
          description: Failed to retrieve user data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/user/users:
    get:
      tags:
        - Users
      summary: Get users statistics
      description: Retrieve online users and subscription statistics
      security:
        - sessionAuth: []
      responses:
        "200":
          description: Users statistics retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  total_users:
                    type: integer
                    example: 1500
                  online_users:
                    type: integer
                    example: 45
                  users:
                    type: array
                    items:
                      $ref: "#/components/schemas/UserAccount"
        "400":
          description: Failed to retrieve users statistics
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/user/history:
    get:
      tags:
        - Users
      summary: Get download history
      description: Retrieve user's download history
      security:
        - sessionAuth: []
      responses:
        "200":
          description: Download history retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  downloads:
                    type: array
                    items:
                      type: object
                      properties:
                        from:
                          type: string
                          example: "freepik"
                        type:
                          type: string
                          enum: ["photo", "video", "vector"]
                          example: "photo"
                        price:
                          type: integer
                          example: 1
                        date:
                          type: string
                          format: date
                          example: "2025-01-05"
                        file:
                          type: string
                          format: uri
                          example: "https://www.freepik.com/free-photo/example"
        "400":
          description: Failed to retrieve download history
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Pricing Management Endpoints
  /v1/pricing/add:
    post:
      tags:
        - Pricing
      summary: Add pricing plan
      description: Create a new pricing plan
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PricingPlanInput"
      responses:
        "200":
          description: Pricing plan added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Pricing added successfully."
        "400":
          description: Failed to add pricing plan
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/pricing/edit:
    post:
      tags:
        - Pricing
      summary: Edit pricing plan
      description: Update an existing pricing plan
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PricingPlanInput"
      responses:
        "200":
          description: Pricing plan updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Pricing plan updated successfully."
        "400":
          description: Failed to update pricing plan
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/pricing/delete:
    post:
      tags:
        - Pricing
      summary: Delete pricing plan
      description: Remove a pricing plan
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - PlanName
              properties:
                PlanName:
                  type: string
                  example: "premium"
      responses:
        "200":
          description: Pricing plan deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Pricing plan deleted successfully."
        "400":
          description: Failed to delete pricing plan
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/pricing/get:
    get:
      tags:
        - Pricing
      summary: Get pricing plans
      description: Retrieve all available pricing plans
      responses:
        "200":
          description: Pricing plans retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/PricingPlan"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  # Site Management Endpoints
  /v1/sites/add:
    post:
      tags:
        - Sites
      summary: Add site
      description: Add a new downloadable site
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SiteInput"
      responses:
        "200":
          description: Site added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "Site added successfully."
                      site:
                        $ref: "#/components/schemas/SiteResponse"
        "400":
          description: Failed to add site
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/sites/edit:
    post:
      tags:
        - Sites
      summary: Edit site
      description: Update an existing site
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SiteInput"
      responses:
        "200":
          description: Site updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "Site edited successfully."
                      site:
                        $ref: "#/components/schemas/SiteResponse"
        "400":
          description: Failed to update site
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/sites/delete:
    post:
      tags:
        - Sites
      summary: Delete site
      description: Remove a site
      security:
        - sessionAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - SiteUrl
              properties:
                SiteUrl:
                  type: string
                  format: uri
                  example: "https://example.com"
      responses:
        "200":
          description: Site deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: "Site deleted successfully."
                      site:
                        type: object
                        properties:
                          Url:
                            type: string
                            format: uri
        "400":
          description: Failed to delete site
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /v1/sites/get:
    get:
      tags:
        - Sites
      summary: Get sites
      description: Retrieve all available sites
      security:
        - sessionAuth: []
      responses:
        "200":
          description: Sites retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      sites:
                        type: array
                        items:
                          $ref: "#/components/schemas/Site"
        "400":
          description: Failed to retrieve sites
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

components:
  securitySchemes:
    sessionAuth:
      type: apiKey
      in: cookie
      name: session
      description: Session cookie obtained from login endpoint

  schemas:
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            id:
              oneOf:
                - type: integer
                - type: string
              example: 1
            message:
              type: string
              example: "Error description"
      required:
        - success
        - error

    UserAccount:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        picture:
          type: string
          format: uri
          example: "https://h.top4top.io/p_3339n2wgn1.jpg"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
      required:
        - email
        - firstName
        - lastName

    Subscription:
      type: object
      properties:
        active:
          type: boolean
          example: true
        plan:
          type: string
          nullable: true
          example: "premium"
        credits:
          type: object
          properties:
            remaining:
              type: integer
              example: 150
            plan:
              type: integer
              example: 200
          required:
            - remaining
            - plan
        until:
          type: string
          format: date-time
          nullable: true
          example: "2024-02-05 14:30:00"
        allowed_sites:
          type: array
          items:
            type: string
          example: ["freepik", "shutterstock"]
      required:
        - active
        - credits
        - allowed_sites

    CreditHistoryEntry:
      type: object
      properties:
        type:
          type: string
          enum: ["add", "use", "delete"]
          example: "use"
        amount:
          type: integer
          example: 5
        timestamp:
          type: string
          format: date-time
          example: "2024-01-05 14:30:00"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        user_name:
          type: string
          example: "John Doe"
        plan:
          type: string
          example: "premium"
        reason:
          type: string
          example: "download"
        previous_plan:
          type: string
          description: "Only present for upgrade entries"
          example: "basic"
      required:
        - type
        - amount
        - timestamp
        - email
        - user_name
        - plan

    PricingPlan:
      type: object
      properties:
        name:
          type: string
          example: "premium"
        price:
          type: string
          nullable: true
          example: "29.99"
        days:
          type: string
          example: "30"
        description:
          type: string
          nullable: true
          example: "Premium plan with unlimited downloads"
        contact:
          type: string
          nullable: true
          format: uri
          example: "https://example.com/contact"
        credits:
          type: string
          example: "200"
        sites:
          type: array
          items:
            type: string
          example: ["freepik", "shutterstock", "unsplash"]
      required:
        - name
        - days
        - credits
        - sites

    PricingPlanInput:
      type: object
      required:
        - PlanName
        - DaysValidity
        - Sites
        - PlanDescription
        - credits
      properties:
        PlanName:
          type: string
          example: "premium"
        PlanPrice:
          type: string
          example: "29.99"
        DaysValidity:
          type: string
          example: "30"
        Sites:
          type: array
          items:
            type: string
          example: ["freepik", "shutterstock"]
        PlanDescription:
          type: string
          example: "Premium plan with unlimited downloads"
        ContactUsUrl:
          type: string
          format: uri
          example: "https://example.com/contact"
        credits:
          type: string
          example: "200"

    Site:
      type: object
      properties:
        name:
          type: string
          example: "Freepik"
        url:
          type: string
          example: "freepik.com"
        icon:
          type: string
          format: uri
          nullable: true
          example: "https://example.com/icon.png"
        total_downloads:
          type: integer
          example: 1500
        today_downloads:
          type: integer
          example: 25
        price:
          type: integer
          example: 1
        last_reset:
          type: string
          format: date
          example: "2024-01-05"
      required:
        - name
        - url
        - total_downloads
        - today_downloads
        - price
        - last_reset

    SiteInput:
      type: object
      required:
        - SiteName
        - SiteUrl
      properties:
        SiteName:
          type: string
          example: "Freepik"
        SiteUrl:
          type: string
          format: uri
          example: "https://freepik.com"
        price:
          type: string
          default: "1"
          example: "2"
        icon:
          type: string
          format: uri
          nullable: true
          example: "https://example.com/icon.png"

    SiteResponse:
      type: object
      properties:
        name:
          type: string
          example: "Freepik"
        url:
          type: string
          format: uri
          example: "https://freepik.com"
        price:
          type: string
          example: "1"
      required:
        - name
        - url
        - price

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Credits
    description: Credit and subscription management
  - name: Users
    description: User data and statistics
  - name: Pricing
    description: Pricing plan management
  - name: Sites
    description: Downloadable site management
