"use client";

import {
  CalendarDays,
  CreditCard,
  Download,
  Filter,
  TrendingUp,
  Settings,
  Activity,
  Search,
  Lock,
  Eye,
  EyeOff,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState, useEffect, useCallback } from "react";
import {
  ProfilePageSkeleton,
  DownloadHistoryItemSkeleton,
} from "@/components/profile-page-skeletons";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { useLanguage } from "@/components/i18n-provider";
import { HeaderControls } from "@/components/header-controls";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/components/auth-provider";
import { useRouter } from "next/navigation";
import { userApi, type DownloadHistoryEntry } from "@/lib/api";

// Utility function to get the correct media URL based on environment and type
const getMediaUrl = (item: DownloadHistoryEntry): string => {
  console.log("[Profile] Processing media URL for item:", {
    from: item.from,
    type: item.type,
    file: item.file,
    downloadUrl: item.downloadUrl,
  });

  // For local files (mock data), use them directly - they work on both localhost and Vercel
  if (item.file.startsWith("/")) {
    console.log("[Profile] Using local file:", item.file);
    return item.file; // This will be "/freepik-1.jpg" from mock data
  }

  // For external URLs (Freepik, etc.), use the media proxy
  if (item.file.startsWith("http")) {
    const proxyUrl = `/api/media-proxy?url=${encodeURIComponent(item.file)}`;
    console.log("[Profile] Using media proxy:", proxyUrl);
    return proxyUrl;
  }

  // Fallback to the original file URL
  console.log("[Profile] Using fallback URL:", item.file);
  return item.file;
};

// Utility function to check if the item is a video
const isVideoItem = (item: DownloadHistoryEntry): boolean => {
  return item.type === "video" || item.file.includes("video");
};

export default function ProfilePage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const [sortFilter, setSortFilter] = useState("newest");
  const [isProfileLoading, setIsProfileLoading] = useState(true);
  const [isDownloadHistoryLoading, setIsDownloadHistoryLoading] =
    useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [downloadHistory, setDownloadHistory] = useState<
    DownloadHistoryEntry[]
  >([]);
  const [downloadHistoryError, setDownloadHistoryError] = useState<
    string | null
  >(null);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
  const [imageLoading, setImageLoading] = useState<Set<number>>(new Set());
  const { isRTL, isLoading } = useLanguage();
  const { t } = useTranslation("common");

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [authLoading, isAuthenticated, router]);

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);

  // Password change handlers
  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const togglePasswordVisibility = (field: "current" | "new" | "confirm") => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      return;
    }

    if (passwordData.newPassword.length < 8) {
      return;
    }

    setIsPasswordLoading(true);

    try {
      // Password change API implementation would go here
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch {
      // Error handling would go here
    } finally {
      setIsPasswordLoading(false);
    }
  };

  // Load download history
  const loadDownloadHistory = useCallback(async () => {
    if (!isAuthenticated) return;

    setIsDownloadHistoryLoading(true);
    setDownloadHistoryError(null);
    try {
      const response = await userApi.getDownloadHistory();

      if (response.success) {
        // Handle both response structures: response.data.downloads and direct response.downloads
        if (response.data && response.data.downloads) {
          setDownloadHistory(response.data.downloads);
        } else if (
          "downloads" in response &&
          Array.isArray(response.downloads)
        ) {
          setDownloadHistory(response.downloads as DownloadHistoryEntry[]);
        } else {
          setDownloadHistory([]);
        }
      } else {
        const errorMessage =
          response.error?.message || "Failed to load download history";
        setDownloadHistoryError(errorMessage);
        setDownloadHistory([]);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Network error occurred";
      setDownloadHistoryError(errorMessage);
      setDownloadHistory([]);
    } finally {
      setIsDownloadHistoryLoading(false);
    }
  }, [isAuthenticated]);

  // Load data when authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      setIsProfileLoading(false);
      loadDownloadHistory();
    }
  }, [isAuthenticated, user, loadDownloadHistory]);

  // Reset image states when download history changes
  useEffect(() => {
    setImageErrors(new Set());
    // Set all images as loading initially
    const loadingSet = new Set<number>();
    downloadHistory.forEach((_, index) => {
      loadingSet.add(index);
    });
    setImageLoading(loadingSet);
  }, [downloadHistory]);

  // Show loading skeleton while authentication, language data or profile data is loading
  if (authLoading || isLoading || isProfileLoading) {
    return <ProfilePageSkeleton />;
  }

  // Show loading if not authenticated (will redirect)
  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Filter downloads based on search query
  const filteredDownloads = downloadHistory.filter((item) => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase();
    return (
      item.from.toLowerCase().includes(query) ||
      item.type.toLowerCase().includes(query) ||
      item.file.toLowerCase().includes(query)
    );
  });

  const sortedDownloads = [...filteredDownloads].sort((a, b) => {
    switch (sortFilter) {
      case "newest":
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case "oldest":
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      case "credits-high":
        return b.price - a.price;
      case "credits-low":
        return a.price - b.price;
      default:
        return 0;
    }
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Calculate credits percentage
  const totalCredits = user?.subscription?.credits?.plan || 0;
  const remainingCredits = user?.subscription?.credits?.remaining || 0;
  const usedCredits = totalCredits - remainingCredits;
  const creditsUsedPercentage =
    totalCredits > 0 ? (usedCredits / totalCredits) * 100 : 0;

  // Get user display name
  const getUserDisplayName = () => {
    if (user?.account?.firstName && user?.account?.lastName) {
      return `${user.account.firstName} ${user.account.lastName}`;
    }
    return user?.account?.email || "User";
  };

  // Get user initials
  const getUserInitials = () => {
    if (user?.account?.firstName && user?.account?.lastName) {
      return `${user.account.firstName.charAt(0)}${user.account.lastName.charAt(0)}`.toUpperCase();
    }
    return user?.account?.email?.charAt(0).toUpperCase() || "U";
  };

  return (
    <div
      className={`min-h-screen bg-secondary/50 ${isRTL ? "font-tajawal" : "font-sans"}`}
    >
      {/* Header */}
      <header className="bg-background border-b border-border">
        <header className="px-4 sm:px-5 py-4">
          <div className="flex items-center justify-between">
            <Link
              href="/"
              className={`flex items-center ${isRTL ? "space-x-reverse !space-x-2" : "space-x-2"}`}
            >
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-primary-foreground rounded-sm"></div>
              </div>
              <span className="text-base sm:text-xl font-semibold text-foreground">
                {t("header.logo")}
              </span>
            </Link>
            <HeaderControls />
          </div>
        </header>
      </header>
      {/* Main Content */}
      <main className="px-5 py-6 sm:py-8 space-y-4 sm:space-y-5">
        {/* User Info Section */}
        <Card className="overflow-hidden dark:bg-muted/50 border-none shadow-xs py-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
          <div className="relative p-6 sm:p-8">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 opacity-50"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-primary/5 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-secondary/5 rounded-full blur-2xl"></div>
            {/* Content */}
            <div className="relative z-10">
              <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6 sm:gap-8">
                {/* Avatar Section */}
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
                  <Avatar className="relative h-24 w-24 sm:h-28 sm:w-28 border-4 border-background/80 backdrop-blur-sm">
                    <AvatarImage
                      src={user?.account?.picture || ""}
                      alt={getUserDisplayName()}
                      className="object-cover"
                    />
                    <AvatarFallback className="text-xl sm:text-2xl font-bold bg-gradient-to-br from-primary to-primary/80 text-primary-foreground">
                      {getUserInitials()}
                    </AvatarFallback>
                  </Avatar>
                  {/* Online Status Indicator */}
                  <div className="absolute bottom-1 right-1 w-6 h-6 bg-green-500 border-4 border-background rounded-full animate-pulse"></div>
                </div>
                {/* User Info */}
                <div className="text-center sm:text-left flex-1 space-y-4">
                  <div className="space-y-2 flex flex-col">
                    <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground tracking-tight">
                      {getUserDisplayName()}
                    </h1>
                    <p className="text-base sm:text-lg text-muted-foreground font-medium">
                      {user?.account?.email}
                    </p>
                    {/* Status Badges */}
                    <div>
                      <Badge
                        variant="outline"
                        className="px-4 py-2 text-sm font-semibold bg-secondary/80 hover:bg-secondary transition-colors"
                      >
                        <CreditCard
                          className={`w-4 h-4 ${isRTL ? "ml-2" : "mr-2"}`}
                        />
                        {user?.subscription?.plan || "Free"}{" "}
                        {t("profile.userInfo.member")}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5">
          {/* Total Downloads Card */}
          <Card className="group dark:bg-muted/50">
            <CardContent>
              <div className="flex items-start justify-between">
                <div className="space-y-3 sm:space-y-4 flex-1">
                  <div
                    className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
                  >
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                      <Download className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                        {t("profile.stats.totalDownloads.title")}
                      </h3>
                      <p className="text-xs sm:text-sm text-muted-foreground/80">
                        {t("profile.stats.totalDownloads.description")}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div
                      className={`flex items-baseline ${isRTL ? "space-x-reverse !space-x-2" : "space-x-2"}`}
                    >
                      <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                        {downloadHistory.length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Credits Used Card */}
          <Card className="group dark:bg-muted/50">
            <CardContent>
              <div className="flex items-start justify-between">
                <div className="space-y-3 sm:space-y-4 flex-1">
                  <div
                    className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
                  >
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                      <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                        {t("profile.stats.creditsUsed.title")}
                      </h3>
                      <p className="text-xs sm:text-sm text-muted-foreground/80">
                        {t("profile.stats.creditsUsed.description")}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div
                      className={`flex items-baseline ${isRTL ? "space-x-reverse !space-x-2" : "space-x-2"}`}
                    >
                      <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                        {usedCredits}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Credits Remaining Card */}
          <Card className="group dark:bg-muted/50">
            <CardContent>
              <div className="flex items-start justify-between">
                <div className="space-y-3 sm:space-y-4 flex-1">
                  <div
                    className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
                  >
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                      <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                        {t("profile.stats.creditsRemaining.title")}
                      </h3>
                      <p className="text-xs sm:text-sm text-muted-foreground/80">
                        {t("profile.stats.creditsRemaining.description")}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div
                      className={`flex items-baseline ${isRTL ? "space-x-reverse !space-x-2" : "space-x-2"}`}
                    >
                      <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                        {remainingCredits}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Plan Status Card */}
          <Card className="group dark:bg-muted/50">
            <CardContent>
              <div className="flex items-start justify-between">
                <div className="space-y-3 sm:space-y-4 flex-1">
                  <div
                    className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
                  >
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center relative">
                      <Activity className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                      {user?.subscription?.active && (
                        <div
                          className={`absolute -top-1 w-3 h-3 bg-green-500 rounded-full animate-pulse ${isRTL ? "-left-1" : "-right-1"}`}
                        ></div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                        {t("profile.stats.planStatus.title")}
                      </h3>
                      <p className="text-xs sm:text-sm text-muted-foreground/80">
                        {t("profile.stats.planStatus.description")}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div
                      className={`flex items-baseline ${isRTL ? "space-x-reverse !space-x-2" : "space-x-2"}`}
                    >
                      <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors capitalize">
                        {t("profile.userInfo.active")}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        {/* Subscription, Credits & Change Password Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-5">
          {/* Subscription Card */}
          <Card className="dark:bg-muted/50">
            <CardHeader>
              <div
                className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
              >
                <div className="w-10 h-10 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {t("profile.subscription.title")}
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">
                    {t("profile.subscription.description")}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    {t("profile.subscription.currentPlan")}
                  </span>
                  <Badge variant="outline" className="font-medium">
                    {user?.subscription?.plan || "Free"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    {t("profile.subscription.status")}
                  </span>
                  <Badge
                    variant={
                      user?.subscription?.active ? "default" : "destructive"
                    }
                    className={
                      user?.subscription?.active
                        ? "bg-green-100 text-green-800 border border-green-200"
                        : ""
                    }
                  >
                    {t("profile.userInfo.active")}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-foreground">
                    {t("profile.subscription.validUntil")}
                  </span>
                  <div
                    className={`flex items-center gap-1 text-sm text-foreground ${isRTL ? "flex-row-reverse" : ""}`}
                  >
                    <CalendarDays className="h-4 w-4" />
                    {user?.subscription?.until
                      ? formatDate(user.subscription.until)
                      : "N/A"}
                  </div>
                </div>
              </div>
              <Separator />
              <Button className="w-full" variant="outline">
                <Settings className="w-4 h-4" />
                {t("profile.subscription.manageSubscription")}
              </Button>
            </CardContent>
          </Card>

          {/* Credits Card */}
          <Card className="dark:bg-muted/50">
            <CardHeader>
              <div
                className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
              >
                <div className="w-10 h-10 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {t("profile.credits.title")}
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">
                    {t("profile.credits.description")}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex justify-center">
                <div className="text-center space-y-2 w-fit bg-secondary/75 dark:bg-secondary  p-4 rounded-xl">
                  <div className="text-3xl font-bold text-primary">
                    {remainingCredits}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {t("profile.credits.remaining")}
                  </p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-foreground">
                    {t("profile.credits.usageProgress")}
                  </span>
                  <span className="font-medium text-foreground">
                    {Math.round(creditsUsedPercentage)}%
                  </span>
                </div>
                <Progress value={creditsUsedPercentage} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>
                    {usedCredits} {t("profile.credits.used")}
                  </span>
                  <span>
                    {totalCredits} {t("profile.credits.total")}
                  </span>
                </div>
              </div>
              <Separator />
              <Button className="w-full">
                <CreditCard className="w-4 h-4" />
                {t("profile.credits.buyMore")}
              </Button>
            </CardContent>
          </Card>

          {/* Change Password Card */}
          <Card className="dark:bg-muted/50">
            <CardHeader>
              <div
                className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
              >
                <div className="w-10 h-10 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                  <Lock className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-foreground">
                    {t("profile.changePassword.title")}
                  </CardTitle>
                  <p className="text-xs text-muted-foreground">
                    {t("profile.changePassword.description")}
                  </p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <form onSubmit={handlePasswordSubmit} className="space-y-6">
                <div className="space-y-4">
                  {/* Current Password */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="currentPassword"
                      className="text-sm font-medium text-foreground"
                    >
                      {t("profile.changePassword.currentPassword")}
                    </Label>
                    <div className="relative">
                      <Input
                        id="currentPassword"
                        type={showPasswords.current ? "text" : "password"}
                        value={passwordData.currentPassword}
                        onChange={(e) =>
                          handlePasswordChange(
                            "currentPassword",
                            e.target.value
                          )
                        }
                        placeholder={t(
                          "profile.changePassword.currentPasswordPlaceholder"
                        )}
                        className={`${isRTL ? "pr-10 pl-3" : "pl-3 pr-10"}`}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className={`absolute top-1/2 transform -translate-y-1/2 h-8 w-8 ${isRTL ? "left-1" : "right-1"}`}
                        onClick={() => togglePasswordVisibility("current")}
                      >
                        {showPasswords.current ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* New Password */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="newPassword"
                      className="text-sm font-medium text-foreground"
                    >
                      {t("profile.changePassword.newPassword")}
                    </Label>
                    <div className="relative">
                      <Input
                        id="newPassword"
                        type={showPasswords.new ? "text" : "password"}
                        value={passwordData.newPassword}
                        onChange={(e) =>
                          handlePasswordChange("newPassword", e.target.value)
                        }
                        placeholder={t(
                          "profile.changePassword.newPasswordPlaceholder"
                        )}
                        className={`${isRTL ? "pr-10 pl-3" : "pl-3 pr-10"}`}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className={`absolute top-1/2 transform -translate-y-1/2 h-8 w-8 ${isRTL ? "left-1" : "right-1"}`}
                        onClick={() => togglePasswordVisibility("new")}
                      >
                        {showPasswords.new ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Confirm Password */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="confirmPassword"
                      className="text-sm font-medium text-foreground"
                    >
                      {t("profile.changePassword.confirmPassword")}
                    </Label>
                    <div className="relative">
                      <Input
                        id="confirmPassword"
                        type={showPasswords.confirm ? "text" : "password"}
                        value={passwordData.confirmPassword}
                        onChange={(e) =>
                          handlePasswordChange(
                            "confirmPassword",
                            e.target.value
                          )
                        }
                        placeholder={t(
                          "profile.changePassword.confirmPasswordPlaceholder"
                        )}
                        className={`${isRTL ? "pr-10 pl-3" : "pl-3 pr-10"}`}
                        required
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className={`absolute top-1/2 transform -translate-y-1/2 h-8 w-8 ${isRTL ? "left-1" : "right-1"}`}
                        onClick={() => togglePasswordVisibility("confirm")}
                      >
                        {showPasswords.confirm ? (
                          <EyeOff className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Password Requirements */}
                  <p className="text-xs text-muted-foreground">
                    {t("profile.changePassword.passwordRequirements")}
                  </p>
                </div>

                <Separator />

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isPasswordLoading}
                >
                  {isPasswordLoading ? (
                    <div className="w-4 h-4 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Lock className="w-4 h-4" />
                  )}
                  {t("profile.changePassword.updatePassword")}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
        {/* Download History - Full Width */}
        <Card className="dark:bg-muted/50 h-fit">
          <CardHeader className="space-y-6">
            <div
              className={`flex items-center ${isRTL ? "space-x-reverse !space-x-3" : "space-x-3"}`}
            >
              <div className="w-10 h-10 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                <Download className="w-5 h-5 text-primary" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg font-semibold text-foreground">
                  {t("profile.downloadHistory.title")}
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground mt-1">
                  {t("profile.downloadHistory.description")}
                </CardDescription>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row items-center gap-5">
              {/* Search Bar */}
              <div className="relative flex-1 w-full">
                <Search
                  className={`absolute top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground ${isRTL ? "right-3" : "left-3"}`}
                />
                <Input
                  type="text"
                  placeholder="Source / Source ID / URL / Tag / Debug ID"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`${isRTL ? "pr-10 pl-3" : "pl-10 pr-3"} h-10`}
                />
              </div>
              {/* Filter */}
              <div className="flex sm:justify-end w-full sm:w-auto">
                <Select value={sortFilter} onValueChange={setSortFilter}>
                  <SelectTrigger className="w-full sm:w-48">
                    <Filter className="w-4 h-4" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">
                      {t("profile.downloadHistory.filters.newest")}
                    </SelectItem>
                    <SelectItem value="oldest">
                      {t("profile.downloadHistory.filters.oldest")}
                    </SelectItem>
                    <SelectItem value="credits-high">
                      {t("profile.downloadHistory.filters.creditsHigh")}
                    </SelectItem>
                    <SelectItem value="credits-low">
                      {t("profile.downloadHistory.filters.creditsLow")}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isDownloadHistoryLoading ? (
              // Show loading skeletons while download history is loading
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Array.from({ length: 4 }, (_, i) => (
                  <DownloadHistoryItemSkeleton key={i} isRTL={isRTL} />
                ))}
              </div>
            ) : downloadHistoryError ? (
              <div className="py-12 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-20 h-20 bg-destructive/10 rounded-full flex items-center justify-center">
                    <Download className="w-9 h-9 text-destructive" />
                  </div>
                  <div>
                    <p className="text-lg sm:text-2xl font-medium text-foreground">
                      Error Loading Download History
                    </p>
                    <p className="text-base sm:text-lg pt-2 text-muted-foreground">
                      {downloadHistoryError}
                    </p>
                    <Button
                      variant="outline"
                      onClick={loadDownloadHistory}
                      className="mt-4"
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              </div>
            ) : sortedDownloads.length === 0 ? (
              <div className="py-12 text-center">
                <div className="flex flex-col items-center space-y-3">
                  <div className="w-20 h-20 bg-muted rounded-full flex items-center justify-center">
                    <Download className="w-9 h-9 text-muted-foreground" />
                  </div>
                  <div>
                    <p className="text-lg sm:text-2xl font-medium text-foreground">
                      {t("profile.downloadHistory.empty.title")}
                    </p>
                    <p className="text-base sm:text-lg pt-2 text-muted-foreground">
                      {t("profile.downloadHistory.empty.description")}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-h-[450px] overflow-y-auto">
                {sortedDownloads.map((item, index) => (
                  <div
                    key={index}
                    className="bg-secondary/50 dark:bg-muted border rounded-lg p-4 space-y-4 hover:bg-muted/50 transition-all duration-200"
                  >
                    {/* Header with source and debug ID */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">
                          {item.type === "photo"
                            ? "📷"
                            : item.type === "video"
                              ? "🎥"
                              : "🎨"}
                        </span>
                        <span className="font-medium text-foreground">
                          {item.from}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {formatDate(item.date)}
                        </span>
                      </div>
                    </div>
                    {/* File ID and format info */}
                    <div className="flex-col sm:flex-row flex items-center gap-5 justify-between">
                      <Button
                        variant="link"
                        className="p-0 h-auto text-blue-600 hover:text-blue-800 truncate max-w-full w-full sm:w-auto sm:max-w-[200px] md:max-w-[250px] text-left justify-start"
                        onClick={() => window.open(item.file, "_blank")}
                      >
                        <span className="truncate block">{item.file}</span>
                      </Button>
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className="text-xs">
                          {item.type}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {item.price} credits
                        </span>
                      </div>
                    </div>
                    {/* Main Media Display */}
                    <div
                      className="relative aspect-video bg-muted rounded-lg overflow-hidden cursor-pointer group"
                      onClick={() =>
                        window.open(item.downloadUrl || item.file, "_blank")
                      }
                    >
                      {imageErrors.has(index) ? (
                        // Fallback display when media fails to load
                        <div className="w-full h-full flex items-center justify-center bg-muted/50">
                          <div className="text-center space-y-2">
                            <div className="text-4xl">
                              {item.type === "photo"
                                ? "📷"
                                : item.type === "video"
                                  ? "🎥"
                                  : "🎨"}
                            </div>
                            <p className="text-sm text-muted-foreground capitalize font-medium">
                              {item.type}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Click to view on {item.from}
                            </p>
                            <p className="text-xs text-muted-foreground/70">
                              Image failed to load
                            </p>
                          </div>
                        </div>
                      ) : (
                        <>
                          {/* Loading skeleton */}
                          {imageLoading.has(index) && (
                            <div className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center">
                              <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
                            </div>
                          )}
                          {isVideoItem(item) ? (
                            // Video preview with play button overlay
                            <div className="relative w-full h-full">
                              <Image
                                src={getMediaUrl(item)}
                                alt={`${item.type} from ${item.from}`}
                                fill
                                className="object-contain group-hover:scale-105 transition-transform duration-300"
                                onLoadingComplete={() => {
                                  setImageLoading((prev) => {
                                    const newSet = new Set(prev);
                                    newSet.delete(index);
                                    return newSet;
                                  });
                                }}
                                onError={(e) => {
                                  console.log(
                                    "[Profile] Image load error for item:",
                                    {
                                      index,
                                      item,
                                      src: getMediaUrl(item),
                                      error: e,
                                    }
                                  );
                                  setImageErrors((prev) =>
                                    new Set(prev).add(index)
                                  );
                                  setImageLoading((prev) => {
                                    const newSet = new Set(prev);
                                    newSet.delete(index);
                                    return newSet;
                                  });
                                }}
                              />
                              {/* Video play button overlay */}
                              <div className="absolute inset-0 flex items-center justify-center bg-black/20 group-hover:bg-black/30 transition-colors">
                                <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-lg">
                                  <div className="w-0 h-0 border-l-[12px] border-l-black border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                                </div>
                              </div>
                            </div>
                          ) : (
                            // Regular image display
                            <Image
                              src={getMediaUrl(item)}
                              alt={`${item.type} from ${item.from}`}
                              fill
                              className="object-contain group-hover:scale-105 transition-transform duration-300"
                              onLoadingComplete={() => {
                                setImageLoading((prev) => {
                                  const newSet = new Set(prev);
                                  newSet.delete(index);
                                  return newSet;
                                });
                              }}
                              onError={(e) => {
                                console.log(
                                  "[Profile] Image load error for item:",
                                  {
                                    index,
                                    item,
                                    src: getMediaUrl(item),
                                    error: e,
                                  }
                                );
                                setImageErrors((prev) =>
                                  new Set(prev).add(index)
                                );
                                setImageLoading((prev) => {
                                  const newSet = new Set(prev);
                                  newSet.delete(index);
                                  return newSet;
                                });
                              }}
                            />
                          )}
                        </>
                      )}
                      {/* Type Badge Overlay */}
                      <div className="absolute top-2 right-2 z-10">
                        <Badge
                          variant="secondary"
                          className="text-xs bg-black/70 text-white border-none"
                        >
                          {item.type}
                        </Badge>
                      </div>
                      {/* Video duration badge for videos */}
                      {isVideoItem(item) && !imageErrors.has(index) && (
                        <div className="absolute bottom-2 right-2 z-10">
                          <Badge
                            variant="secondary"
                            className="text-xs bg-black/70 text-white border-none"
                          >
                            🎥 Video
                          </Badge>
                        </div>
                      )}
                    </div>
                    {/* Footer with source and download button */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {item.from}
                      </span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          window.open(item.downloadUrl || item.file, "_blank")
                        }
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        {t("common.download")}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
            {!isDownloadHistoryLoading && sortedDownloads.length > 4 && (
              <div className="mt-6 text-center">
                <Button variant="outline">
                  {t("profile.downloadHistory.loadMore")}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
