// Mock data for development and testing
import {
  UserData,
  DownloadHistoryEntry,
  ApiResponse,
  LoginResponse,
  RegisterResponse,
  DownloadHistoryResponse,
  CreditStatistics,
  CreditAnalyticsResponse,
  CreditHistoryEntry,
  CreditHistoryResponse,
  UsersStatisticsResponse,
  SitesResponse,
  Site,
  GetPricingPlansResponse,
  PricingPlan,
  PricingPlanResponse,
  PricingPlanInput,
} from "./api";

// Mock user data - Regular user
export const mockUser: UserData = {
  account: {
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "User",
    picture: "/placeholder.svg",
  },
  subscription: {
    plan: "Premium",
    active: true,
    until: "2024-12-31",
    credits: {
      plan: 1000,
      remaining: 750,
    },
    allowed_sites: ["shutterstock", "freepik", "unsplash", "pexels"],
  },
  role: "user",
};

// Mock admin user data
export const mockAdminUser: UserData = {
  account: {
    email: "<EMAIL>",
    firstName: "Admin",
    lastName: "User",
    picture: "/placeholder.svg",
  },
  subscription: {
    plan: "Enterprise",
    active: true,
    until: "2025-12-31",
    credits: {
      plan: 10000,
      remaining: 9500,
    },
    allowed_sites: [
      "shutterstock",
      "freepik",
      "unsplash",
      "pexels",
      "adobe",
      "getty",
    ],
  },
  role: "admin",
};

// Mock download history
export const mockDownloadHistory: DownloadHistoryEntry[] = [
  {
    from: "freepik",
    type: "photo",
    price: 2,
    date: "2024-01-15",
    file: "/image-1.jpg", // Use local image that exists in public folder
    downloadUrl:
      "https://www.freepik.com/free-photo/side-view-hand-wearing-bracelet_31842933.htm",
  },
  {
    from: "freepik",
    type: "video",
    price: 5,
    date: "2024-01-14",
    file: "/image-2.webp", // Use local image for video preview
    downloadUrl:
      "https://www.freepik.com/free-video/close-up-cat-s-face-eyes_171159",
  },
  {
    from: "freepik",
    type: "vector",
    price: 3,
    date: "2024-01-13",
    file: "/freepik-1.jpg", // Keep this one as it exists
    downloadUrl:
      "https://www.freepik.com/free-vector/flat-design-spring-landscape-concept_6718313.htm",
  },
  {
    from: "freepik",
    type: "photo",
    price: 1,
    date: "2024-01-12",
    file: "/office.webp", // Add another local image
    downloadUrl:
      "https://www.freepik.com/free-photo/modern-office-space_12345678.htm",
  },
  {
    from: "freepik",
    type: "vector",
    price: 2,
    date: "2024-01-11",
    file: "/placeholder.png", // Add placeholder image
    downloadUrl:
      "https://www.freepik.com/free-vector/business-illustration_87654321.htm",
  },
];

// Mock credit analytics data
export const mockCreditAnalytics: CreditStatistics = {
  total_credits_issued: 50000,
  total_credits_used: 32500,
  total_remaining_credits: 17500,
  average_daily_usage: 125,
  credits_by_plan: {
    "Basic Plan": 5000,
    "Premium Plan": 15000,
    "Enterprise Plan": 30000,
  },
  last_updated: new Date().toISOString(),
};

// Mock credit history data
export const mockCreditHistory: CreditHistoryEntry[] = [
  {
    id: 1,
    user_email: "<EMAIL>",
    action: "subscription_added",
    credits_changed: 1000,
    credits_before: 0,
    credits_after: 1000,
    plan_name: "Premium Plan",
    timestamp: "2024-01-15T10:30:00Z",
    description: "New subscription added",
  },
  {
    id: 2,
    user_email: "<EMAIL>",
    action: "download",
    credits_changed: -5,
    credits_before: 500,
    credits_after: 495,
    timestamp: "2024-01-15T11:45:00Z",
    description: "Downloaded image from Freepik",
  },
  {
    id: 3,
    user_email: "<EMAIL>",
    action: "subscription_upgraded",
    credits_changed: 2000,
    credits_before: 800,
    credits_after: 2800,
    plan_name: "Enterprise Plan",
    timestamp: "2024-01-14T14:20:00Z",
    description: "Upgraded to Enterprise Plan",
  },
];

// Mock sites data
export const mockSites: Site[] = [
  {
    name: "Freepik",
    url: "https://freepik.com",
    icon: "https://freepik.com/favicon.ico",
    total_downloads: 15420,
    today_downloads: 89,
    price: 2,
    last_reset: "2024-01-15",
  },
  {
    name: "Shutterstock",
    url: "https://shutterstock.com",
    icon: "https://shutterstock.com/favicon.ico",
    total_downloads: 8750,
    today_downloads: 45,
    price: 3,
    last_reset: "2024-01-15",
  },
  {
    name: "Unsplash",
    url: "https://unsplash.com",
    icon: "https://unsplash.com/favicon.ico",
    total_downloads: 12300,
    today_downloads: 67,
    price: 1,
    last_reset: "2024-01-15",
  },
];

// Mock pricing plans data
export const mockPricingPlans: PricingPlan[] = [
  {
    id: 1,
    name: "Basic Plan",
    description: "Perfect for individuals getting started",
    price: "9.99",
    credits: 100,
    daysValidity: 30,
    contactUsUrl: "https://example.com/contact",
    supportedSites: ["Freepik", "Unsplash"],
    features: ["Access to basic sites", "Email support", "Monthly credits"],
  },
  {
    id: 2,
    name: "Premium Plan",
    description: "Great for professionals and small teams",
    price: "29.99",
    credits: 500,
    daysValidity: 30,
    contactUsUrl: "https://example.com/contact",
    supportedSites: ["Freepik", "Shutterstock", "Unsplash"],
    features: [
      "Access to all sites",
      "Priority support",
      "Monthly credits",
      "Download history",
    ],
  },
  {
    id: 3,
    name: "Enterprise Plan",
    description: "For large teams and organizations",
    credits: 2000,
    daysValidity: 30,
    contactUsUrl: "https://example.com/contact",
    supportedSites: ["Freepik", "Shutterstock", "Unsplash", "Adobe Stock"],
    features: [
      "Access to all sites",
      "24/7 support",
      "Monthly credits",
      "Team management",
      "Analytics",
    ],
  },
];

// Mock API responses
// Test credentials:
// Regular user: <EMAIL> / password
// Admin user: <EMAIL> / admin
export const mockApiResponses = {
  // Login response
  login: (
    email: string,
    password: string
  ): Promise<ApiResponse<LoginResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (
          (email === "<EMAIL>" && password === "password") ||
          (email === "<EMAIL>" && password === "admin")
        ) {
          // Store the current user email for mock data
          setCurrentMockUserEmail(email);

          resolve({
            success: true,
            data: {
              access_token: "mock_access_token_12345",
              email: email,
              message: "Login successful",
            },
          });
        } else {
          resolve({
            success: false,
            error: {
              id: "invalid_credentials",
              message: "Invalid email or password",
            },
          });
        }
      }, 1000); // Simulate network delay
    });
  },

  // Register response
  register: (userData: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }): Promise<ApiResponse<RegisterResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            access_token: "mock_access_token_12345",
            email: userData.email,
            message: "Registration successful",
          },
        });
      }, 1500);
    });
  },

  // Get user data response
  getUserData: (): Promise<ApiResponse<UserData>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Check which user is currently logged in based on stored email
        const currentUserEmail = getCurrentMockUserEmail();
        const userData =
          currentUserEmail === "<EMAIL>" ? mockAdminUser : mockUser;

        resolve({
          success: true,
          data: userData,
        });
      }, 500);
    });
  },

  // Get download history response
  getDownloadHistory: (): Promise<ApiResponse<DownloadHistoryResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            success: true,
            downloads: mockDownloadHistory,
          },
        });
      }, 800);
    });
  },

  // Logout response
  logout: (): Promise<
    ApiResponse<{ access_token: string; message: string }>
  > => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Clear the current user email for mock data
        clearCurrentMockUserEmail();

        resolve({
          success: true,
          data: {
            access_token: "",
            message: "Logout successful",
          },
        });
      }, 300);
    });
  },

  // Get credit analytics response
  getCreditAnalytics: (): Promise<ApiResponse<CreditAnalyticsResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            success: true,
            statistics: mockCreditAnalytics,
          },
        });
      }, 800);
    });
  },

  // Get credit history response
  getCreditHistory: (): Promise<ApiResponse<CreditHistoryResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            success: true,
            history: mockCreditHistory,
          },
        });
      }, 600);
    });
  },

  // Get users statistics response
  getUsersStatistics: (): Promise<ApiResponse<UsersStatisticsResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            success: true,
            total_users: 1250,
            online_users: 89,
            users: [
              {
                email: "<EMAIL>",
                firstName: "John",
                lastName: "Doe",
                picture: "/placeholder.svg",
              },
              {
                email: "<EMAIL>",
                firstName: "Jane",
                lastName: "Smith",
                picture: "/placeholder.svg",
              },
              {
                email: "<EMAIL>",
                firstName: "Mike",
                lastName: "Johnson",
                picture: "/placeholder.svg",
              },
            ],
          },
        });
      }, 700);
    });
  },

  // Get sites response
  getSites: (): Promise<ApiResponse<SitesResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            success: true,
            data: {
              sites: mockSites,
            },
          },
        });
      }, 500);
    });
  },

  // Get pricing plans response
  getPricingPlans: (): Promise<ApiResponse<GetPricingPlansResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            success: true,
            data: mockPricingPlans,
          },
        });
      }, 600);
    });
  },

  // Add pricing plan response
  addPricingPlan: (
    data: PricingPlanInput
  ): Promise<ApiResponse<PricingPlanResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Add the new plan to mock data
        const newPlan: PricingPlan = {
          id: mockPricingPlans.length + 1,
          name: data.PlanName,
          description: data.PlanDescription,
          price: data.PlanPrice,
          credits: parseInt(data.credits),
          daysValidity: parseInt(data.DaysValidity),
          contactUsUrl: data.ContactUsUrl || "",
          supportedSites: data.Sites,
          features: [
            "Access to supported sites",
            "24/7 Support",
            "Admin Management",
          ],
        };
        mockPricingPlans.push(newPlan);

        resolve({
          success: true,
          data: {
            success: true,
            message: "Pricing plan added successfully.",
          },
        });
      }, 800);
    });
  },

  // Edit pricing plan response
  editPricingPlan: (
    data: PricingPlanInput
  ): Promise<ApiResponse<PricingPlanResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Find and update the plan in mock data
        const planIndex = mockPricingPlans.findIndex(
          (plan) => plan.name === data.PlanName
        );
        if (planIndex !== -1) {
          mockPricingPlans[planIndex] = {
            ...mockPricingPlans[planIndex],
            name: data.PlanName,
            description: data.PlanDescription,
            price: data.PlanPrice,
            credits: parseInt(data.credits),
            daysValidity: parseInt(data.DaysValidity),
            contactUsUrl: data.ContactUsUrl || "",
            supportedSites: data.Sites,
          };
        }

        resolve({
          success: true,
          data: {
            success: true,
            message: "Pricing plan updated successfully.",
          },
        });
      }, 800);
    });
  },

  // Delete pricing plan response
  deletePricingPlan: (
    planName: string
  ): Promise<ApiResponse<PricingPlanResponse>> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Remove the plan from mock data
        const planIndex = mockPricingPlans.findIndex(
          (plan) => plan.name === planName
        );
        if (planIndex !== -1) {
          mockPricingPlans.splice(planIndex, 1);
        }

        resolve({
          success: true,
          data: {
            success: true,
            message: "Pricing plan deleted successfully.",
          },
        });
      }, 800);
    });
  },
};

// Helper function to check if mock data should be used
export function shouldUseMockData(): boolean {
  // In production, never use mock data unless explicitly enabled
  if (process.env.NODE_ENV === "production") {
    const useMock = process.env.NEXT_PUBLIC_USE_MOCK_DATA === "true";
    console.log("[Mock Data] Production mode - using mock data:", useMock);
    return useMock;
  }

  // In development, use mock data based on environment variable
  const useMock = process.env.NEXT_PUBLIC_USE_MOCK_DATA !== "false";
  console.log("[Mock Data] Development mode - using mock data:", useMock);
  return useMock;
}

// Helper function to simulate network delay
export function simulateNetworkDelay(ms: number = 1000): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// Helper functions to track current mock user
export function setCurrentMockUserEmail(email: string): void {
  if (typeof window !== "undefined") {
    sessionStorage.setItem("mock_current_user_email", email);
  }
}

export function getCurrentMockUserEmail(): string {
  if (typeof window !== "undefined") {
    return (
      sessionStorage.getItem("mock_current_user_email") || "<EMAIL>"
    );
  }
  return "<EMAIL>";
}

export function clearCurrentMockUserEmail(): void {
  if (typeof window !== "undefined") {
    sessionStorage.removeItem("mock_current_user_email");
  }
}
